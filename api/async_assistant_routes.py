"""
Async API routes for the OpenAI Assistant API.
"""
from fastapi import <PERSON><PERSON><PERSON><PERSON>, Request, HTTPEx<PERSON>, Depends, Header
from fastapi.responses import StreamingResponse
from pydantic import BaseModel, Field
from typing import Optional
from services.async_assistant_service import AsyncAssistantService
from utils.logging import logger
from middleware.rate_limiting import async_rate_limit
from middleware.auth import async_require_auth
from middleware.quota import async_quota_limit
import time

# Create a router for FastAPI assistant routes
router = APIRouter()
assistant_service = AsyncAssistantService()

# Pydantic models for request/response
class AssistantQueryRequest(BaseModel):
    query: str = Field(..., description="User's question or message")
    thread_id: Optional[str] = Field(None, description="Optional thread ID for continuing a conversation")
    model: str = Field("gpt-4-turbo-preview", description="Model name to use")
    stream: bool = Field(False, description="Whether to stream the response")

@router.post('/assistant')
@async_require_auth
@async_quota_limit('assistant')
async def assistant_query(request: AssistantQueryRequest, auth0_sub: str = Header(None)):
    """
    Handle authenticated assistant API queries asynchronously.
    Requires auth0_sub in request headers.
    
    Args:
        request: The query request
        auth0_sub: User identifier from request header
        
    Returns:
        JSON response with assistant's answer
    """
    try:
        # If streaming is requested, use streaming response
        if request.stream:
            return await stream_assistant_response(request.query, request.thread_id, request.model, auth0_sub)
        
        # Otherwise, process the query normally with auth0_sub for thread persistence
        result = await assistant_service.query(
            user_query=request.query,
            thread_id=request.thread_id,
            model=request.model,
            auth0_sub=auth0_sub
        )
        
        # Check for errors
        if result.get('error'):
            raise HTTPException(status_code=500, detail=result.get('error'))
        
        return result
        
    except Exception as e:
        logger.error(f"Error in async assistant query: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post('/pub/assistant')
async def public_assistant_query(request: AssistantQueryRequest):
    """
    Handle public (unauthenticated) assistant API queries asynchronously.
    
    Args:
        request: The query request
        
    Returns:
        JSON response with assistant's answer
    """
    try:
        # If streaming is requested, use streaming response
        if request.stream:
            return await stream_assistant_response(request.query, request.thread_id, request.model)
        
        # Otherwise, process the query normally without auth0_sub (no thread persistence)
        result = await assistant_service.query(
            user_query=request.query,
            thread_id=request.thread_id,
            model=request.model
        )
        
        # Check for errors
        if result.get('error'):
            raise HTTPException(status_code=500, detail=result.get('error'))
        
        return result
        
    except Exception as e:
        logger.error(f"Error in async assistant query: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post('/assistant/stream')
@async_require_auth
@async_quota_limit('assistant')
async def assistant_stream_post(request: AssistantQueryRequest, auth0_sub: str = Header(None)):
    """
    Stream the authenticated assistant response (POST method).
    
    Args:
        request: The query request
        auth0_sub: User identifier from request header
        
    Returns:
        Streaming response with assistant's answer
    """
    try:
        return await stream_assistant_response(request.query, request.thread_id, request.model, auth0_sub)
    except Exception as e:
        logger.error(f"Error in streaming assistant response: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post('/pub/assistant/stream')
async def public_assistant_stream_post(request: AssistantQueryRequest):
    """
    Stream the public (unauthenticated) assistant response (POST method).
    
    Args:
        request: The query request
        
    Returns:
        Streaming response with assistant's answer
    """
    try:
        return await stream_assistant_response(request.query, request.thread_id, request.model)
    except Exception as e:
        logger.error(f"Error in streaming assistant response: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get('/assistant/stream')
@async_require_auth
async def assistant_stream_get(
    query: str,
    thread_id: Optional[str] = None,
    model: str = "gpt-4-turbo-preview",
    auth0_sub: str = Header(None)
):
    """
    Stream the authenticated assistant response (GET method).
    This endpoint is used by EventSource in the browser.
    
    Args:
        query: The user's query
        thread_id: Optional thread ID for continuing a conversation
        model: The model to use
        auth0_sub: User identifier from request header
        
    Returns:
        Streaming response with assistant's answer
    """
    try:
        return await stream_assistant_response(query, thread_id, model, auth0_sub)
    except Exception as e:
        logger.error(f"Error in streaming assistant response (GET): {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get('/pub/assistant/stream')
async def public_assistant_stream_get(
    query: str,
    thread_id: Optional[str] = None,
    model: str = "gpt-4-turbo-preview"
):
    """
    Stream the public (unauthenticated) assistant response (GET method).
    This endpoint is used by EventSource in the browser.
    
    Args:
        query: The user's query
        thread_id: Optional thread ID for continuing a conversation
        model: The model to use
        
    Returns:
        Streaming response with assistant's answer
    """
    try:
        return await stream_assistant_response(query, thread_id, model)
    except Exception as e:
        logger.error(f"Error in streaming assistant response (GET): {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

async def stream_assistant_response(query, thread_id, model, auth0_sub=None):
    """
    Stream the assistant response.
    
    Args:
        query: The user's query
        thread_id: Optional thread ID for continuing a conversation
        model: The model to use
        auth0_sub: Optional user identifier for thread persistence
        
    Returns:
        Streaming response with assistant's answer
    """
    async def generate():
        try:
            # Send an initial message to establish the connection
            initial_message = {
                "type": "connection_established",
                "thread_id": thread_id,
                "model": model
            }
            logger.info(f"Sending initial message: {json.dumps(initial_message)}")
            yield f"data: {json.dumps(initial_message)}\n\n"
            
            # Get the streaming response generator
            response_generator = await assistant_service.stream_query(
                user_query=query,
                thread_id=thread_id,
                model=model,
                auth0_sub=auth0_sub
            )
            
            # Stream each chunk as it comes in
            async for chunk in response_generator:
                # Log the chunk for debugging
                logger.debug(f"Streaming chunk: {json.dumps(chunk)}")
                
                # Check if this is a metadata message
                if "type" in chunk and chunk["type"] == "metadata":
                    # For metadata messages, transform them to a client-friendly format
                    metadata_message = {
                        "type": "metadata",
                        "reference_urls": chunk.get("reference_urls", []),
                        "follow_up_questions": chunk.get("follow_up_questions", []),
                        "session_id": chunk.get("thread_id", thread_id),
                        "round_id": chunk.get("run_id"),
                        "timestamp": chunk.get("timestamp", time.time())
                    }
                    logger.debug(f"Sending metadata message: {json.dumps(metadata_message)}")
                    yield f"data: {json.dumps(metadata_message)}\n\n"
                # Check if this is a company_facts message
                elif "type" in chunk and chunk["type"] == "company_facts":
                    # For company_facts messages, pass them through directly
                    # This ensures the financial data is sent to the client
                    company_facts_message = {
                        "type": "company_facts",
                        "data": chunk.get("data", {}),
                        "session_id": chunk.get("thread_id", thread_id),
                        "round_id": chunk.get("run_id"),
                        "timestamp": chunk.get("timestamp", time.time())
                    }
                    logger.info(f"Sending company_facts message for financial data")
                    logger.debug(f"Company facts data: {json.dumps(company_facts_message)[:100]}...")
                    yield f"data: {json.dumps(company_facts_message)}\n\n"
                elif "type" in chunk and chunk["type"] == "progress":
                    progress_message = {
                        "type": "progress",
                        "text": chunk.get("content", {}),
                        "session_id": chunk.get("thread_id", thread_id),
                        "round_id": chunk.get("run_id"),
                        "timestamp": chunk.get("timestamp", time.time())
                    }
                    logger.info(f"Sending progress text")
                    yield f"data: {json.dumps(progress_message)}\n\n"
                elif "type" in chunk and chunk["type"] == "user_query_confirmed":
                    # Handle the new user_query_confirmed message type
                    user_query_confirmed_message = {
                        "type": "user_query_confirmed",
                        "value": chunk.get("value"),
                        "session_id": chunk.get("thread_id", thread_id),
                        "timestamp": chunk.get("timestamp", time.time())
                    }
                    logger.info(f"Sending user_query_confirmed message with ID: {chunk.get('value')}")
                    yield f"data: {json.dumps(user_query_confirmed_message)}\n\n"
                elif "type" in chunk and chunk["type"] == "Error":
                    # For error messages, create a special error message format
                    error_message = {
                        "type": "Error",
                        "text": chunk.get("content", "Unknown error"),
                        "error": chunk.get("error", "Unknown error"),
                        "session_id": chunk.get("thread_id", thread_id),
                        "round_id": chunk.get("run_id"),
                        "timestamp": chunk.get("timestamp", time.time())
                    }
                    logger.error(f"Sending error message: {json.dumps(error_message)}")
                    yield f"data: {json.dumps(error_message)}\n\n"
                else:
                    # For regular content chunks, transform as before
                    transformed_chunk = {}
                    transformed_chunk["type"] = "answer"
                    # Extract thread_id and run_id
                    if "thread_id" in chunk:
                        transformed_chunk["session_id"] = chunk["thread_id"]
                    elif thread_id:
                        transformed_chunk["session_id"] = thread_id
                    
                    if "run_id" in chunk:
                        transformed_chunk["round_id"] = chunk["run_id"]
                    
                    # Extract content from different possible formats
                    if "content" in chunk:
                        # Direct content field (new format)
                        transformed_chunk["content"] = chunk["content"]
                    elif "choices" in chunk and chunk["choices"] and "delta" in chunk["choices"][0]:
                        # Old OpenAI format with delta
                        delta = chunk["choices"][0]["delta"]
                        if "content" in delta and delta["content"]:
                            transformed_chunk["content"] = delta["content"]
                    
                    # Always send the chunk, even if content is empty
                    # This ensures the stream stays alive and all messages are forwarded
                    if "content" in transformed_chunk:
                        logger.debug(f"Sending transformed chunk: {json.dumps(transformed_chunk)}")
                        yield f"data: {json.dumps(transformed_chunk)}\n\n"
                    else:
                        # If there's no content, add an empty string to keep the stream alive
                        transformed_chunk["content"] = ""
                        logger.debug(f"Sending empty content chunk: {json.dumps(transformed_chunk)}")
                        yield f"data: {json.dumps(transformed_chunk)}\n\n"
                
                # If we get thread_id or run_id, send a special message with this info
                '''
                if (chunk.get("thread_id") and not thread_id) or chunk.get("run_id"):
                    info_message = {
                        "type": "metadata_update",
                        "session_id": chunk.get("thread_id", thread_id),
                        "round_id": chunk.get("run_id")
                    }
                    logger.debug(f"Sending metadata update: {json.dumps(info_message)}")
                    yield f"data: {json.dumps(info_message)}\n\n"
                '''
                
            # Send a done message to indicate the end of the stream
            done_message = {
                "type": "answer",
                "content": "[DONE]",
                "session_id": thread_id,
                "round_id": None
            }
            logger.info("Sending done message")
            yield f"data: {json.dumps(done_message)}\n\n"
                
        except Exception as error:
            # Use a different variable name to avoid scope issues
            logger.error(f"Error in streaming assistant response: {str(error)}")
            error_data = {
                "error": str(error),
                "content": f"Error: {str(error)}"
            }
            yield f"data: {json.dumps(error_data)}\n\n"
    
    # Import json here to avoid circular imports
    import json
    
    # Return a streaming response with enhanced headers for SSE
    # CORS headers are handled by FastAPI CORS middleware
    return StreamingResponse(
        generate(),
        media_type='text/event-stream',
        headers={
            'Cache-Control': 'no-cache, no-transform',
            'Connection': 'keep-alive',
            'X-Accel-Buffering': 'no',  # Disable buffering in Nginx
            'Content-Type': 'text/event-stream',
            'Transfer-Encoding': 'chunked'
        }
    )

@router.post('/assistant/thread')
@async_require_auth
async def create_thread(auth0_sub: str = Header(None)):
    """
    Create a new conversation thread asynchronously.
    
    Args:
        auth0_sub: User identifier from request header
        
    Returns:
        JSON response with thread ID
    """
    try:
        result = await assistant_service.create_thread()
        
        # If thread was created successfully and auth0_sub is provided, store in Redis
        if result.get('created') and auth0_sub and result.get('thread_id'):
            from utils.async_redis_client import get_async_redis_client
            redis_client = await get_async_redis_client()
            await redis_client.set(f"thread:{auth0_sub}", result.get('thread_id'))
            logger.info(f"Stored thread ID {result.get('thread_id')} for user {auth0_sub} in Redis")
        
        if result.get('error'):
            raise HTTPException(status_code=500, detail=result.get('error'))
        
        return result
        
    except Exception as e:
        logger.error(f"Error creating thread: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post('/pub/assistant/thread')
async def public_create_thread():
    """
    Create a new conversation thread asynchronously (public endpoint).
    
    Returns:
        JSON response with thread ID
    """
    try:
        result = await assistant_service.create_thread()
        
        if result.get('error'):
            raise HTTPException(status_code=500, detail=result.get('error'))
        
        return result
        
    except Exception as e:
        logger.error(f"Error creating thread: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.delete('/assistant/thread/{thread_id}')
@async_require_auth
async def delete_thread(thread_id: str, auth0_sub: str = Header(None)):
    """
    Delete a conversation thread asynchronously.
    
    Args:
        thread_id: The thread ID to delete
        auth0_sub: User identifier from request header
        
    Returns:
        JSON response with result
    """
    try:
        result = await assistant_service.delete_thread(thread_id)
        
        # If thread was deleted successfully and auth0_sub is provided, remove from Redis
        if result.get('deleted') and auth0_sub:
            from utils.async_redis_client import get_async_redis_client
            redis_client = await get_async_redis_client()
            await redis_client.delete(f"thread:{auth0_sub}")
            logger.info(f"Removed thread ID for user {auth0_sub} from Redis")
        
        if result.get('error'):
            raise HTTPException(status_code=500, detail=result.get('error'))
        
        return result
        
    except Exception as e:
        logger.error(f"Error deleting thread {thread_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.delete('/pub/assistant/thread/{thread_id}')
async def public_delete_thread(thread_id: str):
    """
    Delete a conversation thread asynchronously (public endpoint).
    
    Args:
        thread_id: The thread ID to delete
        
    Returns:
        JSON response with result
    """
    try:
        result = await assistant_service.delete_thread(thread_id)
        
        if result.get('error'):
            raise HTTPException(status_code=500, detail=result.get('error'))
        
        return result
        
    except Exception as e:
        logger.error(f"Error deleting thread {thread_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post('/assistant/debug-stream')
async def debug_stream():
    """
    Debug endpoint for testing streaming responses.
    """
    async def generate():
        import json
        import asyncio
        
        # Send a series of test messages
        for i in range(10):
            test_message = {
                "type": "test",
                "index": i,
                "content": f"Test message {i}",
                "thread_id": "test-thread-id",
                "run_id": "test-run-id"
            }
            logger.info(f"Sending debug message {i}: {json.dumps(test_message)}")
            yield f"data: {json.dumps(test_message)}\n\n"
            await asyncio.sleep(0.5)  # Simulate delay between messages
    
    # Return a streaming response with enhanced headers for SSE
    # CORS headers are handled by FastAPI CORS middleware
    return StreamingResponse(
        generate(),
        media_type='text/event-stream',
        headers={
            'Cache-Control': 'no-cache, no-transform',
            'Connection': 'keep-alive',
            'X-Accel-Buffering': 'no',  # Disable buffering in Nginx
            'Content-Type': 'text/event-stream',
            'Transfer-Encoding': 'chunked'
        }
    )

@router.get('/assistant/thread/{thread_id}/messages')
@async_require_auth
async def get_thread_messages(thread_id: str):
    """
    Get all messages in a thread asynchronously.
    
    Args:
        thread_id: The thread ID
        
    Returns:
        JSON response with messages
    """
    try:
        result = await assistant_service.get_thread_messages(thread_id)
        
        if result.get('error'):
            raise HTTPException(status_code=500, detail=result.get('error'))
        
        return result
        
    except Exception as e:
        logger.error(f"Error getting messages for thread {thread_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get('/pub/assistant/thread/{thread_id}/messages')
async def public_get_thread_messages(thread_id: str):
    """
    Get all messages in a thread asynchronously (public endpoint).
    
    Args:
        thread_id: The thread ID
        
    Returns:
        JSON response with messages
    """
    try:
        result = await assistant_service.get_thread_messages(thread_id)
        
        if result.get('error'):
            raise HTTPException(status_code=500, detail=result.get('error'))
        
        return result
        
    except Exception as e:
        logger.error(f"Error getting messages for thread {thread_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post('/assistant/thread/{thread_id}/run/{run_id}/cancel')
@async_require_auth
async def cancel_run(thread_id: str, run_id: str):
    """
    Cancel an in-progress run asynchronously.
    
    Args:
        thread_id: The thread ID
        run_id: The run ID to cancel
        
    Returns:
        JSON response with result
    """
    try:
        result = await assistant_service.cancel_run(thread_id, run_id)
        
        if result.get('error'):
            raise HTTPException(status_code=500, detail=result.get('error'))
        
        return result
        
    except Exception as e:
        logger.error(f"Error cancelling run {run_id} in thread {thread_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post('/pub/assistant/thread/{thread_id}/run/{run_id}/cancel')
async def public_cancel_run(thread_id: str, run_id: str):
    """
    Cancel an in-progress run asynchronously (public endpoint).
    
    Args:
        thread_id: The thread ID
        run_id: The run ID to cancel
        
    Returns:
        JSON response with result
    """
    try:
        result = await assistant_service.cancel_run(thread_id, run_id)
        
        if result.get('error'):
            raise HTTPException(status_code=500, detail=result.get('error'))
        
        return result
        
    except Exception as e:
        logger.error(f"Error cancelling run {run_id} in thread {thread_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post('/assistant/cancel')
@async_require_auth
async def cancel_active_run(auth0_sub: str = Header(None)):
    """
    Cancel the most recent active run for the authenticated user asynchronously.
    
    Args:
        auth0_sub: User identifier from request header
        
    Returns:
        JSON response with result
    """
    try:
        result = await assistant_service.cancel_active_run(auth0_sub=auth0_sub)
        
        if result.get('error'):
            raise HTTPException(status_code=500, detail=result.get('error'))
        
        return result
        
    except Exception as e:
        logger.error(f"Error cancelling active run: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))
